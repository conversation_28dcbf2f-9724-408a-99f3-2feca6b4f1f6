{"version": "2.2.1", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "react-router build", "build:staging": "cross-env NODE_ENV=test react-router build", "dev": "react-router dev", "start:production": "cross-env API_BASE_URL=https://api.triket.arsi.in PORT=3000 NODE_ENV=production react-router-serve build/server/index.js", "start:staging": "cross-env API_BASE_URL=https://api-dev.triket.live PORT=3001 NODE_ENV=test react-router-serve build/server/index.js", "typecheck": "react-router typegen && tsc", "codegen": "graphql-codegen", "watch": "graphql-codegen -w"}, "dependencies": {"@headlessui/react": "^1.7.18", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.0.7", "@react-router/express": "^7.0.0", "@react-router/node": "^7.0.0", "@tanstack/react-query": "^5.69.0", "animejs": "^3.2.2", "blurhash": "^2.0.5", "canvas": "^3.0.0-rc2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "compression": "^1.7.4", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "embla-carousel-autoplay": "^8.0.0", "embla-carousel-react": "^8.0.0", "express": "^4.18.3", "graphql": "^16.8.1", "graphql-request": "^7.1.0", "html5-qrcode": "^2.3.8", "immer": "^10.1.1", "isbot": "^5.1.1", "lucide-react": "^0.356.0", "morgan": "^1.10.0", "nprogress": "^0.2.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-router": "^7.0.0", "source-map-support": "^0.5.21", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.0", "use-immer": "^0.9.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.4", "@parcel/watcher": "^2.4.1", "@react-router/dev": "^7.0.0", "@react-router/fs-routes": "^7.0.0", "@react-router/serve": "^7.0.0", "@types/animejs": "^3.1.13", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/nprogress": "^0.2.3", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.21", "@types/source-map-support": "^0.5.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "autoprefixer": "^10.4.20", "chokidar": "^3.6.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-tailwindcss": "^3.15.0", "nodemon": "^3.1.0", "pm2": "^6.0.5", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "rimraf": "^5.0.5", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.4.2", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.0.1"}, "engines": {"node": ">=18.0.0"}, "volta": {"node": "22.11.0"}}