import { NavLink } from "react-router"
import dayjs from "dayjs"

import { Blurimage, Rupee } from "@components/common"
import { baseUrl, parseTicketPrices } from "@lib/utils"
import type {
  Event,
  EventsQuery,
  EventTicketType,
} from "@/__generated__/graphql"

interface Props {
  data: EventsQuery["events"]["data"]
}

// type CityEvents = {
//   city: string
//   events: Omit<Event, "city">[]
// }

// INFO: for handling more cities
// the best way would probably be to create a new array from data
// and placing events with the same city in a new array with object
// { id, city, events }
const HomeDataList = ({ data }: Props) => {
  const aizawlEvents = data.filter(
    (item) => item.city === "aizawl" || item.city === null
  )
  const shillongEvents = data.filter((item) => item.city === "shillong")

  const nagalandEvents = data.filter((item) => item.city === "nagaland")

  // const homeEvents = Object.values(
  //   data.reduce((result: Record<string, CityEvents>, { city, ...rest }) => {
  //     if (city) {
  //       if (!result[city]) {
  //         result[city] = { city, events: [] }
  //       }
  //
  //       result[city].events.push(rest)
  //       return result
  //     }
  //     // else {
  //     //   if (!result[city]) {
  //     //     result[city] = { city, events: [] }
  //     //   }
  //     //   result[city].events.push(rest)
  //     //   return result
  //     // }
  //   }, {})
  // )

  return (
    <>
      {aizawlEvents.length > 0 ? (
        <EventsByCity data={aizawlEvents} title="Events in Aizawl" />
      ) : null}
      {shillongEvents.length > 0 ? (
        <EventsByCity data={shillongEvents} title="Events in Shillong" />
      ) : null}
      {nagalandEvents.length > 0 ? (
        <EventsByCity data={nagalandEvents} title="Events in Nagaland" />
      ) : null}
    </>
  )
}

interface EventsByCityProps {
  data: EventsQuery["events"]["data"]
  title: string
}

const EventsByCity = ({ data, title }: EventsByCityProps) => {
  return (
    <section className="mx-auto my-8 grid max-w-5xl grid-cols-12 gap-4 px-4 md:my-8 md:gap-16 md:px-10">
      <h2 className="col-span-12 px-8 text-center text-3xl md:text-5xl">
        {title}
      </h2>
      <div className="relative col-span-12 grid grid-cols-12 gap-4 md:gap-16">
        {data.map((item) => {
          let prices = [0]
          if (item.eventTicketTypes != null) {
            prices = item?.eventTicketTypes
              ? parseTicketPrices(item.eventTicketTypes as EventTicketType[])
              : [0]
          }

          return (
            <div key={item.id} className="col-span-6 md:col-span-6">
              <NavLink to={`/event/${item.slug}`}>
                <div className="mx-auto flex w-full flex-col">
                  <div className="flex cursor-pointer justify-center">
                    {item.images && item.images[0]?.path ? (
                      <div className="relative aspect-[4/5] w-full text-clip">
                        <Blurimage
                          objectFit="cover"
                          src={`${baseUrl}/image/medium/${item.images[0].path}`}
                          hash={item.images[0].hash || ""}
                          alt={item.name}
                        />
                        {/* <img */}
                        {/*   className="absolute left-0 top-0 h-full w-full object-cover" */}
                        {/*   src={`${baseUrl}/image/medium/${item.images[0].path}`} */}
                        {/*   alt={`${item.name}`} */}
                        {/* /> */}
                      </div>
                    ) : null}
                  </div>
                  <h2 className="mt-1 text-left text-base font-semibold tracking-tight md:text-3xl">
                    {item.name}
                  </h2>

                  {item?.start_booking_date &&
                  dayjs().isBefore(item.start_booking_date) ? (
                    <>
                      <p className="text-xs text-gray-500">Coming soon</p>
                    </>
                  ) : (
                    <>
                      <p className="text-xs text-gray-500">
                        {dayjs(item?.start_date).format(
                          "Do MMM, YYYY | hh:mm a"
                        )}{" "}
                        <span className="hidden md:inline-block">
                          |{" "}
                          {prices.length > 1 ? (
                            <>
                              <Rupee /> {Math.min(...prices)} onwards
                            </>
                          ) : Math.min(...prices) > 0 ? (
                            <>
                              <Rupee /> {Math.min(...prices)}
                            </>
                          ) : (
                            " Free booking"
                          )}
                        </span>
                      </p>

                      <p className="text-xs text-gray-500 md:hidden">
                        {prices.length > 1 ? (
                          <>
                            <Rupee /> {Math.min(...prices)} onwards
                          </>
                        ) : Math.min(...prices) > 0 ? (
                          <>
                            <Rupee /> {Math.min(...prices)}
                          </>
                        ) : (
                          " Free booking"
                        )}
                      </p>
                    </>
                  )}
                </div>
              </NavLink>
            </div>
          )
        })}
      </div>
    </section>
  )
}

export default HomeDataList
