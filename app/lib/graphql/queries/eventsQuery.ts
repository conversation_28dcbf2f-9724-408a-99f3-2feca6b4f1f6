import { graphql } from "@/__generated__"

const EVENTS_QUERY = graphql(`
  query Events(
    $page: Int!
    $currentDatetime: DateTime!
    $name: String
    $type: String!
    $first: Int!
  ) {
    events(
      page: $page
      current_datetime: $currentDatetime
      name: $name
      type: $type
      first: $first
    ) {
      data {
        id
        name
        start_date
        images {
          id
          path
          hash
          width
          height
        }
        slug
        eventTicketTypes {
          price
          ticket_type
        }
        start_booking_date
        open_mic_link
        city
      }
      paginatorInfo {
        lastPage
        hasMorePages
        currentPage
      }
    }
  }
`)

export default EVENTS_QUERY
