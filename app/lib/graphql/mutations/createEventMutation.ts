import { graphql } from "@/__generated__"

const CREATE_EVENT_MUTATION = graphql(`
  mutation CreateEvent(
    $name: String!
    $description: String
    $location: String!
    $eventTicketType: [CreateEventTicketTypeInput]!
    $eventImages: [Upload]!
    $startDate: DateTime!
    $endDate: DateTime!
    $startBookingDate: DateTime
    $address: String!
    $orgName: String!
    $orgContactNumber: String!
    $isPublished: Boolean!
    $isPrivateEvent: Boolean
    $faq: String
    $artists: [EventArtistsInput]
    $openMicLink: String
    $city: String!
    $eventType: EventType
  ) {
    createEvent(
      name: $name
      description: $description
      location: $location
      event_ticket_types: $eventTicketType
      event_images: $eventImages
      start_date: $startDate
      end_date: $endDate
      start_booking_date: $startBookingDate
      organizer_name: $orgName
      org_contact_number: $orgContactNumber
      address: $address
      is_published: $isPublished
      is_private_event: $isPrivateEvent
      faq: $faq
      artists: $artists
      open_mic_link: $openMicLink
      city: $city
      event_type: $eventType
    ) {
      id
      is_published
    }
  }
`)

export default CREATE_EVENT_MUTATION
