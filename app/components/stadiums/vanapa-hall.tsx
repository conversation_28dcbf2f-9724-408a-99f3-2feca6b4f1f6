import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

const VanapaHall = () => {
  return (
    <div className="grid min-h-[500px] min-w-[1280px] grid-cols-12 gap-1 overflow-scroll text-xs font-bold">
      <div className="col-span-5 ">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 1}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 13}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 25}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 37}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 49}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index === 0 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
              >
                G{index + 49}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => index < 2 ? (<div/>) : (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 49}
            </CheckboxGroupItem>
          ))}
          {/* {Array.from({ length: 12 }, (_, index) => ( */}
          {/*   <CheckboxGroupItem */}
          {/*     key={index + 1} */}
          {/*     variant="seat" */}
          {/*     className="col-span-1 rounded-lg text-[0.5rem]" */}
          {/*   > */}
          {/*     G{index + 49} */}
          {/*   </CheckboxGroupItem> */}
          {/* ))} */}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 49}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 49}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-2" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
            >
              G{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
    </div>
  )
}

export default VanapaHall
